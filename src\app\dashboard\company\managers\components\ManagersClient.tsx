'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Copy,
  CheckCircle,
  AlertCircle,
  Building2,
  UserX,
  Loader2,
} from 'lucide-react';
import { toast } from 'sonner';
import { Gyms, Profiles } from '@/types/database/tables';

import { CreateInvitationDialog } from './CreateInvitationDialog';
import { cancelInvitation } from '@/lib/actions/gym_invitations/invitation-actions';
import { GymManagerInvitationWithDetails } from '@/types/gym-manager-invitations';
import { removeManagerFromGym } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';

// Gelen gym verisine manager profilini de ekliyoruz
type GymWithManager = Gyms & { manager_profile: Profiles | null };

interface ManagersClientProps {
  gyms: GymWithManager[];
  invitations: GymManagerInvitationWithDetails[];
}

export function ManagersClient({ gyms, invitations }: ManagersClientProps) {
  const router = useRouter();
  const [cancellingId, setCancellingId] = useState<string | null>(null);
  const [loadingGymId, setLoadingGymId] = useState<string | null>(null);
  const [removeDialogOpen, setRemoveDialogOpen] = useState(false);
  const [gymToRemove, setGymToRemove] = useState<{
    id: string;
    name: string;
    managerName: string;
  } | null>(null);

  const handleCancelInvitation = async (invitationId: string) => {
    setCancellingId(invitationId);
    const result = await cancelInvitation(invitationId);
    if (result.success) {
      toast.success('Davet kodu başarıyla iptal edildi.');
      router.refresh();
    } else {
      toast.error(
        result.error || 'Davet kodu iptal edilirken bir hata oluştu.'
      );
    }
    setCancellingId(null);
  };

  const handleRemoveManagerClick = (gym: GymWithManager) => {
    if (gym.manager_profile) {
      setGymToRemove({
        id: gym.id,
        name: gym.name,
        managerName: gym.manager_profile.full_name || 'Bilinmeyen Yönetici',
      });
      setRemoveDialogOpen(true);
    }
  };

  const handleRemoveManagerConfirm = async () => {
    if (!gymToRemove) return;

    setLoadingGymId(gymToRemove.id);
    const result = await removeManagerFromGym({ gymId: gymToRemove.id });
    if (result.success) {
      toast.success('Yönetici başarıyla kaldırıldı.');
      router.refresh();
    } else {
      toast.error(result.error || 'Yönetici kaldırılırken bir hata oluştu.');
    }
    setLoadingGymId(null);
    setRemoveDialogOpen(false);
    setGymToRemove(null);
  };

  const copyInviteCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Davet kodu kopyalandı!');
  };

  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleString('tr-TR');

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Yönetici Atama</h1>
        <p className="text-muted-foreground mt-1">
          Salonlarınıza yönetici atayın, mevcut yöneticileri değiştirin ve davet
          kodlarını yönetin.
        </p>
      </div>

      {/* Salonlar Listesi */}
      <Card>
        <CardHeader>
          <CardTitle>Salonlar</CardTitle>
          <CardDescription>
            Şirketinize bağlı salonlar ve atanmış yöneticileri.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {gyms.map(gym => (
              <div
                key={gym.id}
                className="flex items-center justify-between rounded-lg border p-4"
              >
                <div className="flex items-center gap-4">
                  <Building2 className="text-muted-foreground h-8 w-8" />
                  <div>
                    <p className="font-semibold">{gym.name}</p>
                    {gym.manager_profile ? (
                      <div className="text-muted-foreground flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>
                          {gym.manager_profile.full_name} (
                          {gym.manager_profile.email})
                        </span>
                      </div>
                    ) : (
                      <div className="text-muted-foreground flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        <span>Yönetici atanmamış</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-end">
                  {gym.manager_profile ? (
                    <Button
                      variant="destructive"
                      onClick={() => handleRemoveManagerClick(gym)}
                      disabled={loadingGymId === gym.id}
                    >
                      {loadingGymId === gym.id ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <UserX className="mr-2 h-4 w-4" />
                      )}
                      Yöneticiyi Kaldır
                    </Button>
                  ) : (
                    <CreateInvitationDialog
                      gym={{ id: gym.id, name: gym.name }}
                      companyId={gym.company_id!}
                      onSuccess={router.refresh}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Davet Kodları Tablosu */}
      <Card>
        <CardHeader>
          <CardTitle>Aktif Davet Kodları</CardTitle>
          <CardDescription>
            Oluşturulmuş ve henüz kullanılmamış davet kodları.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Salon</TableHead>
                <TableHead>Davet Kodu</TableHead>
                <TableHead>Son Geçerlilik</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations.filter(inv => inv.status === 'pending').length >
              0 ? (
                invitations
                  .filter(inv => inv.status === 'pending')
                  .map(invitation => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">
                        {invitation.gym.name}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <code className="bg-muted rounded px-2 py-1 font-mono">
                            {invitation.invite_code}
                          </code>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              copyInviteCode(invitation.invite_code)
                            }
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(invitation.expires_at)}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCancelInvitation(invitation.id)}
                          disabled={cancellingId === invitation.id}
                        >
                          {cancellingId === invitation.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            'İptal Et'
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Aktif davet kodu bulunmuyor.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Yönetici Kaldırma Onay Dialogu */}
      <AlertDialog open={removeDialogOpen} onOpenChange={setRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yöneticiyi Kaldır</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>{gymToRemove?.managerName}</strong> adlı yöneticiyi{' '}
              <strong>{gymToRemove?.name}</strong> salonundan kaldırmak
              istediğinizden emin misiniz?
              <br />
              <br />
              Bu işlem geri alınamaz ve yönetici artık bu salona erişim
              sağlayamayacaktır.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loadingGymId === gymToRemove?.id}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveManagerConfirm}
              disabled={loadingGymId === gymToRemove?.id}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loadingGymId === gymToRemove?.id ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Kaldırılıyor...
                </>
              ) : (
                'Yöneticiyi Kaldır'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
