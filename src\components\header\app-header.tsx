import { NavLinks } from './nav-links';
import { AppMobileNavigation } from './mobile/app-mobile-navigation';
import { SportivaLogo } from './shared/sportiva-logo';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { UserDropdown } from './user-dropdown';
import { NAV_LINKS } from './shared/header-constants';
import { Button } from '../ui/button';

export async function AppHeader() {
  const res = await getProfile();
  const profile = res.success ? res.data! : null;
  const authenticated = Boolean(profile);

  return (
    <header
      className="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-30 w-full border-b backdrop-blur"
      role="banner"
    >
      <div className="container mx-auto flex h-14 w-full items-center px-2">
        <div className="flex items-center space-x-2 md:space-x-4">
          <AppMobileNavigation />
          <SportivaLogo />
        </div>
        <NavLinks className="mx-auto hidden w-full items-center justify-center space-x-6 md:flex" />
        {authenticated && profile ? (
          <div className="ml-auto flex items-center space-x-2">
            <Link
              href={NAV_LINKS.dashboard}
              className="text-muted-foreground hover:text-primary group mr-4 flex border px-4 py-2 text-xs font-medium"
            >
              <span>Panel</span>
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
            <UserDropdown profile={profile} />
          </div>
        ) : (
          <div className="ml-auto flex items-center space-x-2">
            <Link href={NAV_LINKS.login}>
              <Button variant="ghost" size="sm">
                Giriş Yap
              </Button>
            </Link>
            <Link href={NAV_LINKS.register}>
              <Button size="sm">Kayıt Ol</Button>
            </Link>
          </div>
        )}
      </div>
    </header>
  );
}
