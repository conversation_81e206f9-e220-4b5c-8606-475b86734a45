import fs from 'fs';
import path from 'path';

// Discover static routes from Next.js App Router (src/app)
// - Includes folders containing page.(tsx|ts|jsx|js)
// - Skips dynamic routes ([slug]) and intercepting routes
// - Removes route groups like (public)
export function discoverStaticRoutes(appDir = path.join(process.cwd(), 'src', 'app')): string[] {
  const routes: string[] = [];

  function walk(dir: string, segs: string[] = []) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });

    // If this folder has a page file, record a route for current path
    const hasPage = entries.some(e =>
      e.isFile() && /^(page)\.(t|j)sx?$/.test(e.name)
    );

    if (hasPage) {
      // Build route path from segs, removing route groups and dynamic segments
      const cleanSegs = segs
        .filter(s => !/^\(.*\)$/.test(s)) // remove (group)
        .filter(s => !/[\[\]]/.test(s)); // skip [dynamic]
      const route = '/' + cleanSegs.join('/');
      routes.push(route === '//' ? '/' : route);
    }

    // Recurse into subdirectories
    for (const entry of entries) {
      if (!entry.isDirectory()) continue;
      if (entry.name.startsWith('_')) continue; // ignore special
      const nextDir = path.join(dir, entry.name);
      walk(nextDir, [...segs, entry.name]);
    }
  }

  if (!fs.existsSync(appDir)) return ['/'];
  walk(appDir, []);

  // Deduplicate and sort by depth for stable order
  return Array.from(new Set(routes)).sort((a, b) => a.split('/').length - b.split('/').length || a.localeCompare(b));
}

// Quick manual test when run directly
if (require.main === module) {
  console.log(discoverStaticRoutes());
}

