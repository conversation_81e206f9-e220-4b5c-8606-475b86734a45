import React from 'react';
import { DashboardProvider } from '@/context/dashboard-context';
import { DashboardHeader } from '@/components/header/dashboard-header';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';

// Sunucu bileşeni: dashboard context'i alır ve provider ile sarar.
// PPR için bu bileşen Suspense arkasında çağrılır.
export default async function DashboardContextBoundary({
  children,
}: {
  children: React.ReactNode;
}) {
  const dashboardContext = await getDashboardContext();

  return (
    <DashboardProvider value={dashboardContext}>
      <main
        id="main-content"
        className="relative flex min-h-screen w-full flex-col overflow-hidden"
      >
        <DashboardHeader />
        <div className="h-[calc(100vh-4rem)] overflow-y-auto px-4 py-8 md:pr-10 md:pl-24">
          {children}
        </div>
      </main>
    </DashboardProvider>
  );
}
