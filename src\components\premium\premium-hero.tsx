import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { AnimatedSection } from '@/components/ui/animated-section';
import {
  Shield,
  TrendingUp,
  Building2,
  Sparkles,
  ArrowRight,
  Play,
} from 'lucide-react';

export function PremiumHero() {
  return (
    <section className="from-background via-primary/5 to-background relative isolate overflow-hidden bg-gradient-to-br">
      {/* Enhanced background layers */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/20 to-secondary/10 absolute inset-0 bg-gradient-to-br via-transparent" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,hsl(var(--primary)/0.15),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,hsl(var(--secondary)/0.1),transparent_50%)]" />
        <div className="bg-grid-pattern absolute inset-0 opacity-30" />
      </div>

      <div className="container mx-auto px-4 pt-24 pb-20 md:pt-32">
        <div className="mx-auto max-w-7xl">
          <div className="grid items-center gap-16 lg:grid-cols-2">
            {/* Left */}
            <div className="text-center lg:text-left">
              <AnimatedSection animation="fade-up" delay={250}>
                {/* Premium badge */}
                <div className="border-primary/20 bg-primary/10 text-primary mb-6 inline-flex items-center gap-2 rounded-full border px-4 py-2 text-sm font-medium backdrop-blur-sm">
                  <Sparkles className="h-4 w-4" />
                  <span>Kurumsal Çözüm</span>
                </div>

                <h1 className="text-5xl leading-tight font-extrabold tracking-tight text-balance md:text-6xl lg:text-7xl">
                  <span className="from-foreground via-foreground to-foreground/80 bg-gradient-to-r bg-clip-text text-transparent">
                    Kurumsal Düzeyde
                  </span>
                  <span className="from-primary via-primary to-primary/60 block bg-gradient-to-r bg-clip-text text-transparent">
                    Spor Salonu Yönetimi
                  </span>
                </h1>
                <p className="text-muted-foreground mx-auto mt-6 max-w-[65ch] text-xl text-pretty md:text-2xl lg:mx-0 lg:leading-relaxed">
                  Çoklu şube yönetimi, gelişmiş raporlama ve güvenli altyapı ile
                  operasyonel verimliliğinizi katlayın.
                </p>
              </AnimatedSection>

              <AnimatedSection animation="fade-up" delay={400}>
                <div className="mt-10 flex flex-col items-center gap-4 sm:flex-row sm:justify-center lg:justify-start">
                  <Button
                    asChild
                    size="lg"
                    className="group h-12 px-8 text-base font-semibold shadow-lg transition-all duration-300 hover:shadow-xl"
                  >
                    <Link href="/onboarding" aria-label="Planları gör">
                      Planları Gör
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                  <Button
                    asChild
                    size="lg"
                    variant="outline"
                    className="group hover:bg-primary/5 h-12 border-2 px-8 text-base font-semibold"
                  >
                    <Link href="/features" aria-label="Demo izle">
                      <Play className="mr-2 h-4 w-4" />
                      Demo İzle
                    </Link>
                  </Button>
                </div>

                {/* Enhanced Features */}
                <div className="mt-10 grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="group border-primary/20 from-card/80 to-card/60 hover:border-primary/40 flex items-center gap-3 rounded-xl border bg-gradient-to-r p-5 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                    <div className="bg-primary/10 group-hover:bg-primary/20 rounded-lg p-2 transition-colors">
                      <Shield className="text-primary size-5" />
                    </div>
                    <span className="text-sm font-semibold">
                      KVKK uyumlu güvenlik
                    </span>
                  </div>
                  <div className="group border-primary/20 from-card/80 to-card/60 hover:border-primary/40 flex items-center gap-3 rounded-xl border bg-gradient-to-r p-5 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                    <div className="bg-primary/10 group-hover:bg-primary/20 rounded-lg p-2 transition-colors">
                      <TrendingUp className="text-primary size-5" />
                    </div>
                    <span className="text-sm font-semibold">
                      İleri seviye analitik
                    </span>
                  </div>
                  <div className="group border-primary/20 from-card/80 to-card/60 hover:border-primary/40 flex items-center gap-3 rounded-xl border bg-gradient-to-r p-5 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                    <div className="bg-primary/10 group-hover:bg-primary/20 rounded-lg p-2 transition-colors">
                      <Building2 className="text-primary size-5" />
                    </div>
                    <span className="text-sm font-semibold">
                      Çoklu şube yönetimi
                    </span>
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Enhanced Right mockup */}
            <AnimatedSection
              className="relative"
              animation="slide-left"
              delay={250}
            >
              <div className="relative mx-auto w-full max-w-2xl">
                {/* Floating elements for visual appeal */}
                <div className="from-primary/20 to-secondary/20 absolute -top-4 -right-4 h-20 w-20 rounded-full bg-gradient-to-br blur-xl" />
                <div className="from-secondary/20 to-primary/20 absolute -bottom-4 -left-4 h-16 w-16 rounded-full bg-gradient-to-br blur-xl" />

                <div className="border-primary/20 from-card/90 to-card/70 relative rounded-3xl border bg-gradient-to-br p-6 shadow-2xl backdrop-blur-sm">
                  <div className="border-border/50 bg-background/95 rounded-2xl border p-6 shadow-inner">
                    {/* Header */}
                    <div className="mb-6 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-3 w-3 rounded-full bg-red-400" />
                        <div className="h-3 w-3 rounded-full bg-yellow-400" />
                        <div className="h-3 w-3 rounded-full bg-green-400" />
                      </div>
                      <div className="text-muted-foreground text-xs">
                        Sportiva Dashboard
                      </div>
                    </div>

                    {/* Dashboard mockup */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-3">
                        <div className="from-primary/20 to-primary/10 border-primary/20 h-16 rounded-lg border bg-gradient-to-br" />
                        <div className="from-secondary/20 to-secondary/10 border-secondary/20 h-16 rounded-lg border bg-gradient-to-br" />
                        <div className="from-accent/20 to-accent/10 border-accent/20 h-16 rounded-lg border bg-gradient-to-br" />
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="from-muted/80 to-muted/60 border-border/50 h-20 rounded-lg border bg-gradient-to-br" />
                        <div className="from-muted/80 to-muted/60 border-border/50 h-20 rounded-lg border bg-gradient-to-br" />
                      </div>
                      <div className="from-primary/30 via-primary/20 to-primary/10 border-primary/30 h-12 rounded-lg border bg-gradient-to-r" />
                    </div>

                    {/* Progress indicator */}
                    <div className="mt-6 flex items-center gap-3">
                      <div className="bg-muted h-2 flex-1 rounded-full">
                        <div className="from-primary to-primary/80 h-2 w-3/4 rounded-full bg-gradient-to-r" />
                      </div>
                      <span className="text-muted-foreground text-xs">
                        75% Complete
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  );
}
