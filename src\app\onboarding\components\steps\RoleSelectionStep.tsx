'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { RoleCard, type RoleOption } from '../RoleCard';
import { StepHeader } from '../shared/StepHeader';
import { PlatformRoles } from '@/types/database/enums';

const roleOptions: RoleOption[] = [
  {
    id: 'member' as PlatformRoles,
    title: 'Üye',
    description:
      '<PERSON>lar<PERSON> kat<PERSON>l, antrenmanlarını takip et ve hedeflerine ulaş.',
    icon: <Users className="h-8 w-8" />,
    features: ['Salon üyelikleri', '<PERSON><PERSON><PERSON><PERSON> takibi', '<PERSON><PERSON><PERSON><PERSON> raporları'],
  },
  {
    id: 'trainer' as PlatformRoles,
    title: 'Antrenör',
    description: 'Müşterilerine rehberlik et, antrenman programları oluştur.',
    icon: <Dumbbell className="h-8 w-8" />,
    features: ['Müşteri yönetimi', 'Program oluşturma', '<PERSON><PERSON><PERSON> rapor<PERSON>'],
  },
  {
    id: 'company_manager' as PlatformRoles,
    title: 'Salon Şirketi Oluştur',
    description: 'Salon şirketi kur, birden fazla şube yönet ve büyü.',
    icon: <Crown className="h-8 w-8" />,
    features: ['Çoklu şube yönetimi', 'Merkezi sistem', 'Finansal raporlar'],
  },
];

interface RoleSelectionStepProps {
  selectedRoles: PlatformRoles[];
  isRoleSelectable: (role: PlatformRoles) => boolean;
  getRoleWarningMessage: (role: PlatformRoles) => string | null;
  validateRoleCombination: (roles: PlatformRoles[]) => {
    isValid: boolean;
    message?: string;
  };
  handleRoleToggle: (role: PlatformRoles) => void;
  onContinue: () => void;
  currentRolesSet: Set<PlatformRoles>;
}

export function RoleSelectionStep({
  selectedRoles,
  isRoleSelectable,
  getRoleWarningMessage,
  validateRoleCombination,
  handleRoleToggle,
  onContinue,
  currentRolesSet,
}: RoleSelectionStepProps) {
  const validation = validateRoleCombination(selectedRoles);

  return (
    <>
      <StepHeader
        stepIndex={2}
        totalSteps={5}
        title="Rolünüzü Seçin"
        description="Sportiva'da hangi ek rolleri almak istiyorsunuz? Mevcut rolleriniz korunacak."
        progress={40}
      />
      <div className="mb-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8">
        {roleOptions.map(role => {
          const isCurrentRole = currentRolesSet.has(role.id);
          const canSelect = isRoleSelectable(role.id);
          const isDisabled = isCurrentRole || !canSelect;
          const warningMessage = getRoleWarningMessage(role.id);

          return (
            <div key={role.id} className="relative">
              <RoleCard
                role={role}
                isSelected={selectedRoles.includes(role.id)}
                isDisabled={isDisabled}
                isCurrentRole={isCurrentRole}
                onClick={() => !isDisabled && handleRoleToggle(role.id)}
              />
              {warningMessage && (
                <div className="mt-2 rounded-lg border border-amber-200 bg-amber-50 p-3 text-center dark:border-amber-800 dark:bg-amber-950/30">
                  <span className="text-sm text-amber-700 dark:text-amber-300">
                    ⚠️ {warningMessage}
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {!validation.isValid && (
        <div className="mb-6 rounded-lg border border-red-200 bg-red-50 p-4 text-center dark:border-red-800 dark:bg-red-950/30">
          <span className="text-sm font-medium text-red-700 dark:text-red-300">
            ❌ {validation.message}
          </span>
        </div>
      )}

      <div className="mt-8 flex flex-col items-center gap-6">
        <Button
          onClick={onContinue}
          disabled={selectedRoles.length === 0 || !validation.isValid}
          size="lg"
          className="w-full max-w-xs px-12 py-4 text-lg font-semibold"
        >
          Devam Et <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </>
  );
}
