'use client';

import { RouteError } from '@/components/errors/route-error';

export default function CompanyError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RouteError
      title="Şirket Paneli Hatası"
      description="Şirket paneli içeriği yüklenirken bir hata oluştu."
      error={error}
      reset={reset}
      context={{ route: '/dashboard/company' }}
    />
  );
}
