import { Suspense } from 'react';
import { TrainerInvitationsClient } from './components/trainer-invitations-client';

import {
  IncomingInvitation,
  OutgoingInvitation,
} from '@/lib/actions/gym_invitations/invitation-types';
import {
  getUserIncomingInvites,
  getUserOutgoingRequests,
} from '@/lib/actions/gym_invitations/invitation-actions';

// Force dynamic rendering

// Invitations Data Component
async function InvitationsData() {
  try {
    // Server action ile trainer da<PERSON><PERSON><PERSON> al
    const incomingResult = await getUserIncomingInvites('trainer');
    const outgoingResult = await getUserOutgoingRequests('trainer');

    if (!incomingResult.success || !outgoingResult.success) {
      console.error(
        'Trainer invitations fetch error:',
        incomingResult.error || outgoingResult.error
      );
      return (
        <TrainerInvitationsClient
          incomingInvitations={[]}
          outgoingInvitations={[]}
        />
      );
    }

    // Type assertion ile doğru type'lara çevir
    const incomingInvitations = (incomingResult.data ||
      []) as IncomingInvitation[];
    const outgoingInvitations = (outgoingResult.data ||
      []) as OutgoingInvitation[];

    return (
      <TrainerInvitationsClient
        incomingInvitations={incomingInvitations}
        outgoingInvitations={outgoingInvitations}
      />
    );
  } catch (error) {
    console.error('Invitations fetch error:', error);
    return (
      <TrainerInvitationsClient
        incomingInvitations={[]}
        outgoingInvitations={[]}
      />
    );
  }
}
// Main Page Component
export default async function TrainerInvitationsPage() {
  return (
    <div className="space-y-6">
      <div className="mb-6 border-b pb-6">
        <h1 className="text-3xl font-bold tracking-tight">Salon Davetleri</h1>
        <p className="text-muted-foreground mt-1">
          Size gönderilen salon davetlerini görüntüleyin ve yanıtlayın
        </p>
      </div>

      {/* Invitations List */}
      <Suspense
        fallback={
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-muted h-32 animate-pulse rounded-lg" />
            ))}
          </div>
        }
      >
        <InvitationsData />
      </Suspense>
    </div>
  );
}
