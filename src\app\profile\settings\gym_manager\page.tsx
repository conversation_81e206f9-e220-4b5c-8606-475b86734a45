import { getUserRoles } from '@/lib/auth/server-auth';
import { redirect } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Crown, Settings } from 'lucide-react';

export default async function GymManagerSettingsPage() {
  // Kullanıcının gym_manager rolü olup olma<PERSON>ını kontrol et
  const userRoles = await getUserRoles();
  const isGymManager = userRoles.includes('gym_manager');

  // Gym Manager değilse profil sayfasına yönlendir
  if (!isGymManager) {
    redirect('/profile/settings/profile');
  }

  return (
    <div className="flex-1 space-y-6 p-6 lg:p-8">
      {/* Sayfa başlığı */}
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">
          Salon Yöneticisi Ayarları
        </h1>
        <p className="text-muted-foreground">
          Yönettiğiniz salonlara hızlı erişim ve yönetim bağlantıları
        </p>
      </div>

      {/* İçerik */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="transition-colors">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="text-primary h-5 w-5" />
              Salon Yöneticisi Paneli
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Yönettiğiniz salonları görüntülemek ve yönetmek için paneli
              kullanın.
            </p>
            <Button asChild>
              <Link href="/dashboard/gym_manager">Panele Git</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="transition-colors">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Salon Ayarları
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Her bir salonun detaylı ayarlarını, salon sayfası üzerinden
              düzenleyebilirsiniz.
            </p>
            <p className="text-muted-foreground text-xs">
              İpucu: Gym Manager panelinden bir salon seçip “Ayarlar” ikonuna
              tıklayın.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
