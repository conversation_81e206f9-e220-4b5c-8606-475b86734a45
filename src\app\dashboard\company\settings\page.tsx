import { redirect } from 'next/navigation';
import { getManagerCompany } from '@/lib/actions/all-actions';
import { DashboardHeader } from '@/components/dashboard/shared/dashboard-header';
import { CompanySettingsForm } from './components/CompanySettingsForm';
import { Card, CardContent } from '@/components/ui/card';
import { Building2 } from 'lucide-react';

export default async function ManagerSettingsPage() {
  const companyResponse = await getManagerCompany();

  if (!companyResponse.success || !companyResponse.data) {
    redirect('/dashboard/company');
  }

  const company = companyResponse.data;

  return (
    <div className="space-y-6">
      <DashboardHeader mode="company_manager" />

      <div className="mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <div className="mb-2 flex items-center gap-3">
            <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
              <Building2 className="text-primary h-5 w-5" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{company.name}</h1>
              <p className="text-muted-foreground">Şirket ayarları</p>
            </div>
          </div>
        </div>

        {/* Settings Form */}
        <Card>
          <CardContent>
            <CompanySettingsForm company={company} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
