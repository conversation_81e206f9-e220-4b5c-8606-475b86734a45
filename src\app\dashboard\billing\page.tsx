import { getAllPlatformPackagesCached } from '@/lib/actions/business/platform-packages';
import {
  getManagerDetail,
  getManagerUsageStats,
} from '@/lib/actions/user/manager-actions';
import { ModernBillingClient } from './modern-billing-client';

export const dynamic = 'force-dynamic';

export default async function BillingPage() {
  const [packagesRes, managerRes, usageRes] = await Promise.all([
    getAllPlatformPackagesCached(),
    getManagerDetail(),
    getManagerUsageStats(),
  ]);

  if (!packagesRes.success) {
    throw new Error(packagesRes.error || 'Paketler yüklenemedi');
  }

  if (!managerRes.success) {
    throw new Error(managerRes.error || 'Şirket bilgileri alınamadı');
  }

  const packages = packagesRes.data ?? [];
  const managerDetails = managerRes.data;
  const companyActive = managerDetails?.company?.status === 'active';
  const currentPackageId = managerDetails?.package?.id || null;
  const maxGyms = managerDetails?.package?.max_gyms ?? null;
  const maxMembers = managerDetails?.package?.max_members ?? null;

  const usage =
    usageRes.success && usageRes.data
      ? usageRes.data
      : { gymsUsed: 0, membersUsed: 0, daysRemaining: 0 };

  return (
    <ModernBillingClient
      packages={packages}
      companyActive={companyActive}
      currentPackageId={currentPackageId}
      maxGyms={maxGyms}
      maxMembers={maxMembers}
      usageGyms={usage.gymsUsed}
      usageMembers={usage.membersUsed}
      daysRemaining={usage.daysRemaining}
    />
  );
}
