'use server';

import { ApiResponse } from '@/types/global/api';
import { createAction } from '../core/core';
import { getSupabaseAdmin } from '@/lib/supabase/admin';

/**
 * Yöneticinin mevcut tier'ına göre salon limitini getirir
 */
export async function getManagerGymLimit(): Promise<
  ApiResponse<{ maxGyms: number | null; tier: string }>
> {
  return await createAction<{ maxGyms: number | null; tier: string }>(
    async (_, supabase, authUserId) => {
      // Yöneticinin şirket kaydını user_roles view'ından al (company_id + status)
      const { data: managerData, error: managerError } = await supabase
        .from('user_roles')
        .select('company_id, company_status')
        .eq('profile_id', authUserId)
        .maybeSingle();

      if (managerError) {
        throw new Error(
          `Yönetici bilgileri alınamadı: ${managerError.message}`
        );
      }

      if (
        !managerData ||
        managerData.company_status !== 'active' ||
        !managerData.company_id
      ) {
        throw new Error('Aktif yönetici kaydı bulunamadı');
      }

      // Şirketin bağlı paketinden tier ve limitleri al
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('platform_package_id')
        .eq('id', managerData.company_id)
        .maybeSingle();

      if (companyError || !company) {
        throw new Error('Şirket bilgileri alınamadı');
      }

      const { data: packageData, error: packageError } = await supabase
        .from('platform_packages')
        .select('max_gyms, tier')
        .eq('id', company.platform_package_id)
        .eq('is_active', true)
        .maybeSingle();

      if (packageError) {
        throw new Error(
          `Platform paketi bilgileri alınamadı: ${packageError.message}`
        );
      }

      if (!packageData) {
        throw new Error('Bu tier için aktif paket bulunamadı');
      }

      return {
        maxGyms: (packageData as any).max_gyms,
        tier: (packageData as any).tier,
      };
    }
  );
}

/**
 * Bir salon (gym) için mevcut üye/antrenör sayıları ile maksimum limitleri döndürür
 */
export async function getGymCapacityUsage(gymId: string): Promise<
  ApiResponse<{
    members: { count: number; max: number | null };
    trainers: { count: number; max: number | null };
  }>
> {
  return await createAction<{
    members: { count: number; max: number | null };
    trainers: { count: number; max: number | null };
  }>(async (_, supabase, _authUserId) => {
    const adminClient = getSupabaseAdmin();
    // 1) Gym -> Company -> Platform Package üzerinden limitleri al
    const { data: gym, error: gymError } = await supabase
      .from('gyms')
      .select('id, company_id')
      .eq('id', gymId)
      .single();

    if (gymError || !gym) {
      throw new Error('Salon bilgileri alınamadı');
    }

    const { data: company, error: companyError } = await adminClient
      .from('companies')
      .select('id, platform_package_id')
      .eq('id', gym.company_id)
      .single();

    if (companyError || !company) {
      throw new Error('Şirket bilgileri alınamadı');
    }

    let maxMembers: number | null = null;
    let maxTrainers: number | null = null;

    if (company.platform_package_id) {
      const { data: pkg, error: pkgError } = await supabase
        .from('platform_packages')
        .select('max_members, features')
        .eq('id', company.platform_package_id)
        .single();
      if (pkgError) {
        throw new Error('Paket bilgileri alınamadı');
      }
      maxMembers = (pkg as any)?.max_members ?? null;
      const features = (pkg as any)?.features || {};
      maxTrainers =
        typeof features.max_trainers === 'number'
          ? features.max_trainers
          : null;
    }

    // 2) Mevcut aktif sayıları say
    const { count: memberCount, error: memberCountError } = await supabase
      .from('gym_memberships')
      .select('id', { count: 'exact', head: true })
      .eq('gym_id', gymId)
      .eq('status', 'active');
    if (memberCountError) {
      throw new Error('Üye sayısı alınamadı');
    }

    const { count: trainerCount, error: trainerCountError } = await supabase
      .from('gym_trainers')
      .select('id', { count: 'exact', head: true })
      .eq('gym_id', gymId)
      .eq('status', 'active');
    if (trainerCountError) {
      throw new Error('Antrenör sayısı alınamadı');
    }

    return {
      members: { count: memberCount ?? 0, max: maxMembers },
      trainers: { count: trainerCount ?? 0, max: maxTrainers },
    } as any;
  });
}

/**
 * Yöneticinin mevcut tier'ına göre tüm limitleri getirir
 */
export async function getManagerLimits(): Promise<
  ApiResponse<{
    maxGyms: number | null;
    maxMembers: number | null;
    maxTrainers: number | null;
    tier: string;
  }>
> {
  return await createAction<{
    maxGyms: number | null;
    maxMembers: number | null;
    maxTrainers: number | null;
    tier: string;
  }>(async (_, supabase, authUserId) => {
    // Yöneticinin şirket kaydını user_roles view'ından al (company_id + status)
    const { data: managerData, error: managerError } = await supabase
      .from('user_roles')
      .select('company_id, company_status')
      .eq('profile_id', authUserId)
      .maybeSingle();

    if (managerError) {
      throw new Error(`Yönetici bilgileri alınamadı: ${managerError.message}`);
    }

    if (
      !managerData ||
      managerData.company_status !== 'active' ||
      !managerData.company_id
    ) {
      throw new Error('Aktif yönetici kaydı ya da şirket bilgisi bulunamadı');
    }

    // İlgili şirket -> platform paketi
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('platform_package_id')
      .eq('id', managerData.company_id)
      .maybeSingle();

    if (companyError || !company) {
      throw new Error('Şirket bilgileri alınamadı');
    }

    const { data: packageData, error: packageError } = await supabase
      .from('platform_packages')
      .select('max_gyms, max_members, features')
      .eq('id', company.platform_package_id)
      .eq('is_active', true)
      .maybeSingle();

    if (packageError) {
      throw new Error(
        `Platform paketi bilgileri alınamadı: ${packageError.message}`
      );
    }

    if (!packageData) {
      throw new Error('Bu tier için aktif paket bulunamadı');
    }

    const features: any = (packageData as any).features || {};
    const maxTrainers: number | null =
      typeof features.max_trainers === 'number' ? features.max_trainers : null;

    return {
      maxGyms: (packageData as any).max_gyms ?? null,
      maxMembers: (packageData as any).max_members ?? null,
      maxTrainers: maxTrainers,
      tier: (packageData as any).tier ?? 'unknown',
    } as any;
  });
}
