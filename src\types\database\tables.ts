/**
 * Database table types
 *
 * Supabase table types extracted from the generated schema
 * These are the Row types for each table
 */

import type { Database } from './generated';

// Helper type to extract table types
type Tables<T extends keyof Database['public']['Tables']> =
  Database['public']['Tables'][T]['Row'];

// Exported table types for easy usage
export type Appointments = Tables<'appointments'>;
export type AppointmentParticipants = Tables<'appointment_participants'>;
export type AuditLogs = Tables<'audit_logs'>;
export type Companies = Tables<'companies'>;
export type GymInvitations = Tables<'gym_invitations'>;
export type GymManagerInvitations = Tables<'gym_manager_invitations'>;
export type GymMembershipPackages = Tables<'gym_membership_packages'>;
export type GymMemberships = Tables<'gym_memberships'>;
export type GymPackages = Tables<'gym_packages'>;
export type GymReviews = Tables<'gym_reviews'>;
export type GymStaffs = Tables<'gym_staffs'>;
export type GymTrainers = Tables<'gym_trainers'>;
export type Gyms = Tables<'gyms'>;
export type MemberDetails = Tables<'member_details'>;

// Equipment Management Types
export type EquipmentCategories = Tables<'equipment_categories'>;
export type GymEquipment = Tables<'gym_equipment'>;
export type EquipmentUsageLogs = Tables<'equipment_usage_logs'>;

// Inventory Management Types
export type InventoryCategories = Tables<'inventory_categories'>;
export type GymInventory = Tables<'gym_inventory'>;
export type InventoryTransactions = Tables<'inventory_transactions'>;
export type Notifications = Tables<'notifications'>;
export type PlatformPackages = Tables<'platform_packages'>
export type Profiles = Tables<'profiles'>;
export type TrainerDetails = Tables<'trainer_details'>;
export type UserRoles = Tables<'user_roles'>;
export type UserSettings = Tables<'user_settings'>;
