'use server';

import { getUserRoles } from '@/lib/auth/server-auth';

/**
 * <PERSON><PERSON><PERSON>cının rollerine göre uygun dashboard yolunu döndürür.
 * @returns {Promise<string>} Yönlendirilecek yol.
 */
export async function getDashboardPath(): Promise<string> {
  try {
    const roles = await getUserRoles();

    if (roles.includes('company_manager')) return '/dashboard/company';
    if (roles.includes('gym_manager')) return '/dashboard/gym_manager';
    if (roles.includes('trainer')) return '/dashboard/trainer';
    if (roles.includes('member')) return '/dashboard/member';

    // Eğer hiçbir rolü yoksa ama giriş ya<PERSON>ı<PERSON>, onboarding'e yönlendir
    if (roles.length === 0) return '/onboarding';

    // Beklenmedik bir durum için varsayılan yol
    return '/onboarding';
  } catch (error) {
    // <PERSON><PERSON> doğrulama hatası veya başka bir hata durumunda giriş sayfasına yönlendir
    console.error('Dashboard path alınırken hata:', error);
    return '/auth/login';
  }
}
