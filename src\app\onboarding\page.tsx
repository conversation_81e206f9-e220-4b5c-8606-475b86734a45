import {
  getAuthenticatedUser,
  getUserRoles,
  checkManagerActiveSubscription,
} from '@/lib/auth/server-auth';
import { OnboardingClient } from './components/OnboardingClient';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { PlatformRoles } from '@/types/database/enums';

export const dynamic = 'force-dynamic';

export default async function OnboardingPage({
  searchParams,
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const roles: string[] = await getUserRoles();
  const user = await getAuthenticatedUser();
  const params = await searchParams;

  // Kullanıcının tüm rollere sahip olup olmadığını kontrol et
  const allRoles: PlatformRoles[] = [
    'member',
    'trainer',
    'company_manager',
    'gym_manager',
  ];

  // Eğer kullanıcı manager ve aboneliği aktif <PERSON>, ödeme adımını zorunlu kıl
  const forcePayment =
    !!user &&
    roles.includes('company_manager') &&
    !(await checkManagerActiveSubscription());

  const stepParam = typeof params?.step === 'string' ? params?.step : undefined;

  // Yönlendirme koşulunu hesapla (client tarafında yönlendirilecek)
  // Kullanıcının platform rollerinden en az 3 tanesine sahip olması durumunda dashboard'a yönlendir
  const roleCount = allRoles.filter(r => roles.includes(r)).length;
  const shouldRedirectToDashboard =
    roleCount >= 3 && !forcePayment && stepParam !== 'payment';

  // Kullanıcının profil bilgilerini al
  const profileResult = await getProfile();
  const userProfile =
    profileResult.success && profileResult.data ? profileResult.data : null;

  // Client component'e gerekli verileri props olarak geç
  return (
    <div className="space-y-4">
      <OnboardingClient
        currentRoles={roles as PlatformRoles[]}
        userProfile={userProfile}
        shouldRedirectToDashboard={shouldRedirectToDashboard}
      />
    </div>
  );
}
