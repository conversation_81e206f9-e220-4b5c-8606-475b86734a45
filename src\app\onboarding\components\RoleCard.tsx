'use client';

import { PlatformRoles } from '@/types/database/enums';
import { Check, Lock } from 'lucide-react';

/**
 * Role option interface for role cards
 * Represents a complete role with all display properties
 */
export interface RoleOption {
  id: PlatformRoles;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
}

/**
 * Role card component props
 * Clean interface following single responsibility principle
 */
export interface RoleCardProps {
  role: RoleOption;
  isSelected: boolean;
  isDisabled: boolean;
  isCurrentRole: boolean;
  onClick: () => void;
}

import React from 'react';

function RoleCardBase({
  role,
  isSelected,
  isDisabled,
  isCurrentRole,
  onClick,
}: RoleCardProps) {
  return (
    <div
      className={`group relative overflow-hidden rounded-2xl border-2 bg-gradient-to-br transition-all duration-500 ${
        isDisabled
          ? 'cursor-not-allowed opacity-60'
          : 'cursor-pointer hover:-translate-y-2 hover:shadow-2xl'
      } ${
        isSelected
          ? 'border-primary/50 from-primary/5 via-primary/3 shadow-primary/20 ring-primary/30 to-transparent shadow-xl ring-2'
          : 'border-border/50 from-background via-muted/20 hover:border-primary/30 to-transparent hover:shadow-lg'
      } ${
        role.id === 'member'
          ? 'hover:from-blue-50/50 hover:to-blue-100/30 dark:hover:from-blue-950/30 dark:hover:to-blue-900/20'
          : role.id === 'trainer'
            ? 'hover:from-orange-50/50 hover:to-orange-100/30 dark:hover:from-orange-950/30 dark:hover:to-orange-900/20'
            : 'hover:from-purple-50/50 hover:to-purple-100/30 dark:hover:from-purple-950/30 dark:hover:to-purple-900/20'
      }`}
      onClick={onClick}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-current"></div>
        <div className="absolute -bottom-6 -left-6 h-32 w-32 rounded-full bg-current"></div>
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-4 right-4 z-20">
          <div className="bg-primary text-primary-foreground ring-background flex h-8 w-8 items-center justify-center rounded-full shadow-lg ring-2">
            <Check className="h-4 w-4 font-bold" />
          </div>
        </div>
      )}

      {/* Disabled Indicator */}
      {isDisabled && (
        <div className="absolute top-4 left-4 z-20">
          <div className="bg-muted text-muted-foreground flex h-8 w-8 items-center justify-center rounded-full">
            <Lock className="h-4 w-4" />
          </div>
        </div>
      )}

      <div className="relative z-10 p-6 md:p-8">
        {/* Icon */}
        <div className="mb-6 flex justify-center">
          <div
            className={`rounded-2xl p-4 transition-all duration-300 ${
              isSelected
                ? 'bg-primary/15 text-primary scale-110 shadow-lg'
                : isDisabled
                  ? 'bg-muted/30 text-muted-foreground'
                  : 'bg-muted/50 text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary group-hover:scale-105'
            }`}
          >
            {role.icon}
          </div>
        </div>

        {/* Title & Badge */}
        <div className="mb-4 text-center">
          <h3 className="mb-2 text-xl font-bold tracking-tight md:text-2xl">
            {role.title}
          </h3>
        </div>

        {/* Description */}
        <p className="text-muted-foreground mb-6 text-center text-sm leading-relaxed md:text-base">
          {role.description}
        </p>

        {/* Features */}
        <div className="space-y-3">
          {role.features.slice(0, 3).map((feature: string, index: number) => (
            <div key={index} className="flex items-center gap-3 text-sm">
              <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
                <Check className="h-3 w-3" />
              </div>
              <span className="text-muted-foreground font-medium">
                {feature}
              </span>
            </div>
          ))}

          {role.features.length > 3 && (
            <div className="flex items-center gap-3 text-sm">
              <div className="h-5 w-5 flex-shrink-0"></div>
              <span className="text-muted-foreground/70 font-medium italic">
                +{role.features.length - 3} özellik daha...
              </span>
            </div>
          )}
        </div>

        {/* Current Role Status */}
        {isDisabled && isCurrentRole && (
          <div className="mt-6 rounded-xl border border-green-200 bg-green-50 p-3 text-center dark:border-green-800 dark:bg-green-950/30">
            <span className="text-sm font-semibold text-green-700 dark:text-green-300">
              ✅ Zaten Bu Roldesiniz
            </span>
          </div>
        )}
      </div>

      {/* Selection Glow Effect */}
      {isSelected && (
        <div className="from-primary/10 to-primary/10 absolute inset-0 rounded-2xl bg-gradient-to-r via-transparent opacity-50"></div>
      )}
    </div>
  );
}

export const RoleCard = React.memo(RoleCardBase);
