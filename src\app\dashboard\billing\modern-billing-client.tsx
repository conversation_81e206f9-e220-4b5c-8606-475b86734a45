'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { CheckCircle2, HelpCircle, Star } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import type { PlatformPackages } from '@/types/database/tables';
import { changeCompanyPackage } from '@/lib/actions/business/change-company-package';

interface ModernBillingClientProps {
  packages: PlatformPackages[];
  companyActive: boolean;
  currentPackageId: string | null;
  maxGyms: number | null;
  maxMembers: number | null;
  usageGyms: number;
  usageMembers: number;
  daysRemaining: number;
}

export function ModernBillingClient({
  packages,
  companyActive,
  currentPackageId,
  maxGyms,
  maxMembers,
  usageGyms,
  usageMembers,
  daysRemaining,
}: ModernBillingClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] =
    useState<PlatformPackages | null>(null);

  const currentPackage = packages.find(p => p.id === currentPackageId);

  const handlePackageChange = async (packageId: string) => {
    setIsLoading(packageId);
    try {
      const result = await changeCompanyPackage(packageId);
      if (result.success) {
        toast.success('Paket başarıyla güncellendi!');
        router.refresh();
      } else {
        toast.error(
          result.error || 'Paket güncellenemedi, lütfen tekrar deneyin.'
        );
      }
    } catch (error) {
      toast.error(
        'Beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyin.'
      );
    } finally {
      setIsLoading(null);
      setSelectedPackage(null);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="container mx-auto max-w-7xl px-4 py-12">
        {/* Header */}
        <motion.div variants={itemVariants} className="mb-12 text-center">
          <h1 className="from-primary to-secondary bg-gradient-to-r bg-clip-text text-4xl font-bold tracking-tighter text-transparent md:text-5xl">
            Abonelik ve Faturalandırma
          </h1>
          <p className="text-muted-foreground mx-auto mt-3 max-w-2xl text-lg">
            Planınızı yönetin, kullanımınızı takip edin ve işletmeniz için en
            iyi paketi seçin.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 items-start gap-8 lg:grid-cols-3">
          {/* Left Column: Current Plan & Usage */}
          <motion.div
            variants={itemVariants}
            className="space-y-8 lg:col-span-1"
          >
            <Card className="border-primary/50 border-2 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-2xl">Mevcut Planınız</CardTitle>
                  <Badge
                    variant={companyActive ? 'default' : 'secondary'}
                    className="capitalize"
                  >
                    {companyActive ? 'Aktif' : 'Pasif'}
                  </Badge>
                </div>
                {currentPackage ? (
                  <CardDescription>
                    {currentPackage.name} -{' '}
                    {currentPackage.duration === 'yearly' ? 'Yıllık' : 'Aylık'}{' '}
                    Plan
                  </CardDescription>
                ) : (
                  <CardDescription>
                    Aktif bir aboneliğiniz bulunmuyor.
                  </CardDescription>
                )}
              </CardHeader>
              {currentPackage && (
                <CardContent className="space-y-6">
                  <div className="text-center">
                    <span className="text-4xl font-bold">
                      {currentPackage.price}
                    </span>
                    <span className="text-muted-foreground">
                      {' '}
                      / {currentPackage.duration === 'yearly' ? 'yıl' : 'ay'}
                    </span>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <span className="text-muted-foreground text-sm font-medium">
                          Salonlar
                        </span>
                        <span className="text-sm font-bold">
                          {usageGyms} / {maxGyms ?? '∞'}
                        </span>
                      </div>
                      <Progress
                        value={maxGyms ? (usageGyms / maxGyms) * 100 : 0}
                      />
                    </div>
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <span className="text-muted-foreground text-sm font-medium">
                          Üyeler
                        </span>
                        <span className="text-sm font-bold">
                          {usageMembers} / {maxMembers ?? '∞'}
                        </span>
                      </div>
                      <Progress
                        value={
                          maxMembers ? (usageMembers / maxMembers) * 100 : 0
                        }
                      />
                    </div>
                    <div>
                      <div className="mb-1 flex items-center justify-between">
                        <span className="text-muted-foreground text-sm font-medium">
                          Kalan Süre
                        </span>
                        <span className="text-sm font-bold">
                          {daysRemaining} gün
                        </span>
                      </div>
                      <Progress value={(daysRemaining / 30) * 100} />
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
            <Card className="shadow-md">
              <CardHeader>
                <CardTitle>Yardıma mı ihtiyacınız var?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Aboneliklerle ilgili sorularınız veya endişeleriniz için
                  destek ekibimizle iletişime geçin.
                </p>
                <Button variant="outline">
                  <HelpCircle className="mr-2 h-4 w-4" />
                  Destekle İletişime Geç
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right Column: Available Plans */}
          <motion.div variants={containerVariants} className="lg:col-span-2">
            <h2 className="mb-6 text-3xl font-bold tracking-tight">
              Planınızı Yükseltin
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {packages.map(pkg => {
                const isCurrent = currentPackageId === pkg.id;
                const isRecommended = pkg.name === 'Profesyonel'; // Example recommendation
                return (
                  <motion.div key={pkg.id} variants={itemVariants}>
                    <Card
                      className={cn(
                        'flex h-full flex-col transition-all duration-300 hover:shadow-xl',
                        isCurrent && 'border-primary border-2',
                        isRecommended &&
                          !isCurrent &&
                          'border-secondary border-2'
                      )}
                    >
                      <CardHeader>
                        {isRecommended && (
                          <Badge className="bg-secondary text-secondary-foreground mb-2 w-fit">
                            <Star className="mr-2 h-4 w-4" />
                            Tavsiye Edilen
                          </Badge>
                        )}
                        <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                      </CardHeader>
                      <CardContent className="flex-grow space-y-4">
                        <div className="flex items-baseline">
                          <span className="text-4xl font-bold">
                            {pkg.price}
                          </span>
                          <span className="text-muted-foreground ml-1">
                            / {pkg.duration === 'yearly' ? 'yıl' : 'ay'}
                          </span>
                        </div>
                        <ul className="text-muted-foreground space-y-2">
                          {Array.isArray(pkg.features) &&
                            pkg.features.map((feature, idx) => (
                              <li key={idx} className="flex items-start">
                                <CheckCircle2 className="mt-0.5 mr-2 h-5 w-5 flex-shrink-0 text-green-500" />
                                <span>{String(feature)}</span>
                              </li>
                            ))}
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              className="w-full"
                              variant={isCurrent ? 'outline' : 'default'}
                              disabled={isCurrent}
                              onClick={() => setSelectedPackage(pkg)}
                            >
                              {isCurrent ? 'Mevcut Planınız' : 'Plana Geç'}
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Plan Değişikliğini Onayla
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                {selectedPackage?.name} planına geçmek
                                istediğinizden emin misiniz? Bu işlem hemen
                                uygulanacaktır.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>İptal</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() =>
                                  selectedPackage &&
                                  handlePackageChange(selectedPackage.id)
                                }
                                disabled={isLoading === selectedPackage?.id}
                              >
                                {isLoading === selectedPackage?.id
                                  ? 'Yükseltiliyor...'
                                  : 'Onayla ve Yükselt'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </CardFooter>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
