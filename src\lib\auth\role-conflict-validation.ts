'use server';

import { createAction } from '@/lib/actions/core/core';
import { ApiResponse } from '@/types/global/api';

/**
 * Rol çakışma kontrol sonucu
 */
export interface RoleConflictResult {
  hasCompanyManagerRole: boolean;
  hasGymManagerRole: boolean;
  hasConflict: boolean;
  conflictMessage?: string;
}

/**
 * Kullanıcının mevcut rollerini ve çakışma durumunu kontrol eder
 */
export async function checkUserRoleConflicts(
  userId?: string
): Promise<ApiResponse<RoleConflictResult>> {
  return createAction<RoleConflictResult>(async (_, supabase, authUserId) => {
    const targetUserId = userId || authUserId;

    if (!targetUserId) {
      throw new Error("Kullanıcı ID'si gereklidir");
    }

    // Veritabanındaki fonksiyonu çağır
    const { data, error } = await supabase.rpc('check_user_role_conflicts', {
      user_id: targetUserId,
    });

    if (error) {
      throw new Error(`Rol kontrolü yapılamadı: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return {
        hasCompanyManagerRole: false,
        hasGymManagerRole: false,
        hasConflict: false,
      };
    }

    const result = data[0];
    return {
      hasCompanyManagerRole: result.has_company_manager_role,
      hasGymManagerRole: result.has_gym_manager_role,
      hasConflict: result.has_conflict,
      conflictMessage: result.conflict_message,
    };
  });
}

/**
 * Şirket yöneticisi rolü atanmadan önce çakışma kontrolü
 */
export async function validateCompanyManagerAssignment(
  userId: string
): Promise<ApiResponse<boolean>> {
  return createAction<boolean>(async (_, supabase) => {
    // Kullanıcının gym manager rolü var mı kontrol et
    const { data: gymManagerCheck, error } = await supabase
      .from('gyms')
      .select('id')
      .eq('manager_profile_id', userId)
      .limit(1);

    if (error) {
      throw new Error(`Rol kontrolü yapılamadı: ${error.message}`);
    }

    if (gymManagerCheck && gymManagerCheck.length > 0) {
      throw new Error(
        'Bu kullanıcı zaten salon yöneticisi. Aynı anda hem salon yöneticisi hem şirket yöneticisi olamaz.'
      );
    }

    return true;
  });
}

/**
 * Salon yöneticisi rolü atanmadan önce çakışma kontrolü
 */
export async function validateGymManagerAssignment(
  userId: string
): Promise<ApiResponse<boolean>> {
  return createAction<boolean>(async (_, supabase) => {
    // Kullanıcının company manager rolü var mı kontrol et
    const { data: companyManagerCheck, error } = await supabase
      .from('companies')
      .select('id')
      .eq('manager_profile_id', userId)
      .limit(1);

    if (error) {
      throw new Error(`Rol kontrolü yapılamadı: ${error.message}`);
    }

    if (companyManagerCheck && companyManagerCheck.length > 0) {
      throw new Error(
        'Bu kullanıcı zaten şirket yöneticisi. Aynı anda hem şirket yöneticisi hem salon yöneticisi olamaz.'
      );
    }

    return true;
  });
}

/**
 * Onboarding sırasında rol seçimi validasyonu
 */
export async function validateOnboardingRoleSelection(
  selectedRoles: string[],
  userId?: string
): Promise<ApiResponse<boolean>> {
  return createAction<boolean>(async (_, _supabase, authUserId) => {
    const targetUserId = userId || authUserId;

    if (!targetUserId) {
      throw new Error("Kullanıcı ID'si gereklidir");
    }

    // Seçilen roller arasında çakışma var mı kontrol et
    const hasCompanyManager = selectedRoles.includes('company_manager');
    const hasGymManager = selectedRoles.includes('gym_manager');

    if (hasCompanyManager && hasGymManager) {
      throw new Error(
        'Aynı anda hem şirket yöneticisi hem salon yöneticisi rolü seçilemez.'
      );
    }

    // Mevcut rollerle çakışma var mı kontrol et
    const conflictCheck = await checkUserRoleConflicts(targetUserId);

    if (!conflictCheck.success) {
      throw new Error(conflictCheck.error || 'Rol kontrolü yapılamadı');
    }

    const currentRoles = conflictCheck.data!;

    // Şirket yöneticisi seçilmişse ve kullanıcı zaten salon yöneticisiyse
    if (hasCompanyManager && currentRoles.hasGymManagerRole) {
      throw new Error(
        'Zaten salon yöneticisisiniz. Şirket yöneticisi rolü alamazsınız.'
      );
    }

    // Salon yöneticisi seçilmişse ve kullanıcı zaten şirket yöneticisiyse
    if (hasGymManager && currentRoles.hasCompanyManagerRole) {
      throw new Error(
        'Zaten şirket yöneticisisiniz. Salon yöneticisi rolü alamazsınız.'
      );
    }

    return true;
  });
}

/**
 * Rol çakışması olan kullanıcıları listeler (admin için)
 */
export async function getConflictingUsers(): Promise<ApiResponse<any[]>> {
  return createAction<any[]>(async (_, supabase) => {
    // Hem company hem gym manager olan kullanıcıları bul
    const { data, error } = await supabase
      .from('profiles')
      .select(
        `
          id,
          full_name,
          email,
          companies!inner(id, name),
          gyms!inner(id, name)
        `
      )
      .not('companies', 'is', null)
      .not('gyms', 'is', null);

    if (error) {
      throw new Error(`Çakışan kullanıcılar getirilemedi: ${error.message}`);
    }

    return data || [];
  });
}
