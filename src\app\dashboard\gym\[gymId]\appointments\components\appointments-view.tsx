'use client';

import { AppointmentsList, AppointmentWithDetails } from './appointments-list';
import { AppointmentsCalendar } from './appointments-calendar';
import type { TrainerPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';

// Reuse the list component's accepted shape; keep it loose here
interface AppointmentItem {
  id: string;
  appointment_date: string;
  status?: string | null;
  appointment_type?: string | null;
  max_participants?: number;
  trainer_profile?: { full_name?: string | null };
  participants?: Array<{ id: string }>;
}

interface CalendarExtras {
  showTrainerFilter?: boolean;
  fixedTrainerId?: string;
  permissions?: TrainerPermissions;
}

export function AppointmentsView({
  appointments,
  view = 'list',
  timeSlots,
  calendarExtras,
  mode = 'company_manager',
}: {
  appointments: AppointmentItem[];
  view?: 'list' | 'calendar';
  timeSlots?: string[];
  calendarExtras?: CalendarExtras;
  mode?: 'company_manager' | 'trainer';
}) {
  if (view === 'calendar') {
    return (
      <AppointmentsCalendar
        appointments={appointments}
        timeSlots={timeSlots}
        {...(calendarExtras || {})}
        mode={mode}
      />
    );
  }
  return (
    <AppointmentsList
      appointments={appointments as AppointmentWithDetails[]}
      mode={mode}
      permissions={calendarExtras?.permissions}
    />
  );
}
