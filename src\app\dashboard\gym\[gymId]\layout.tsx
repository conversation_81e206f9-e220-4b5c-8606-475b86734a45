import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';
import { getDashboardContext } from '@/lib/actions/auth/header-auth-actions';

export const metadata: Metadata = {
  title: 'Salon Yönetimi | Sportiva',
  description: 'Sportiva salon yönetim paneli.',
  robots: {
    index: false,
    follow: false,
  },
};

export default async function GymLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { gymId: string };
}) {
  const { gymId } = await params;
  const ctx = await getDashboardContext(gymId); // Salon bağlamı + yetki kontrolü

  if (!ctx.gymAccess?.hasAccess) {
    console.warn('erişim engellendi. dashboarda yönlendiriliyor..');
    redirect('/dashboard');
  }

  // DashboardSidebar'ın beklediği rol formatına çevir
  const sidebarMode = (() => {
    switch (ctx.gymAccess?.role) {
      case 'company_manager':
        return 'company_manager';
      case 'gym_manager':
        return 'gym_manager';
      case 'trainer':
        return 'trainer';
      default:
        return 'member';
    }
  })();

  return (
    <>
      <DashboardSidebar mode={sidebarMode} gymId={gymId} />
      {children}
    </>
  );
}
