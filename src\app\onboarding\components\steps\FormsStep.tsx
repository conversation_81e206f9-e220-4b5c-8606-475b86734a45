'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Building2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { PersonalInfoForm } from '../PersonalInfoForm';
import { MemberForm } from '../MemberForm';
import { TrainerForm } from '../TrainerForm';
import { CompanyForm } from '../CompanyForm';
import { StepHeader } from '../shared/StepHeader';
import type {
  PersonalData,
  MemberData,
  TrainerData,
  ManagerData,
  OnboardingFieldErrors,
} from '@/types/onboarding';
import { PlatformRoles } from '@/types/database/enums';

interface FormsStepProps {
  selectedRoles: PlatformRoles[];
  hasFullName: boolean;
  personalData: PersonalData;
  memberData: MemberData;
  trainerData: TrainerData;
  managerData: ManagerData;
  fieldErrors: OnboardingFieldErrors;
  isSubmitting: boolean;
  isFormValid: boolean;
  onPersonalDataChange: (data: PersonalData) => void;
  onMemberDataChange: (data: MemberData) => void;
  onTrainerDataChange: (data: TrainerData) => void;
  onManagerDataChange: (data: ManagerData) => void;
  onBack: () => void;
  onContinue: () => void;
}

export function FormsStep({
  selectedRoles,
  hasFullName,
  personalData,
  memberData,
  trainerData,
  managerData,
  fieldErrors,
  isSubmitting,
  isFormValid,
  onPersonalDataChange,
  onMemberDataChange,
  onTrainerDataChange,
  onManagerDataChange,
  onBack,
  onContinue,
}: FormsStepProps) {
  return (
    <>
      <StepHeader
        stepIndex={3}
        totalSteps={5}
        title="Bilgilerinizi Tamamlayın"
        description="Seçtiğiniz roller için gerekli bilgileri doldurun."
        progress={60}
      />

      <div className="space-y-8">
        {!hasFullName && (
          <div className="bg-card rounded-lg border p-6">
            <PersonalInfoForm
              data={personalData}
              onChange={onPersonalDataChange}
            />
            {fieldErrors.personal && (
              <p className="mt-2 text-sm text-red-600">
                {fieldErrors.personal}
              </p>
            )}
          </div>
        )}

        {selectedRoles.includes('member') && (
          <div className="bg-card rounded-lg border p-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="text-primary h-5 w-5" /> Üye Bilgileri
              </CardTitle>
            </CardHeader>
            <MemberForm data={memberData} onChange={onMemberDataChange} />
            {fieldErrors.member && (
              <p className="mt-2 text-sm text-red-600">{fieldErrors.member}</p>
            )}
          </div>
        )}

        {selectedRoles.includes('trainer') && (
          <div className="bg-card rounded-lg border p-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Dumbbell className="text-primary h-5 w-5" /> Antrenör Bilgileri
              </CardTitle>
            </CardHeader>
            <TrainerForm data={trainerData} onChange={onTrainerDataChange} />
            {fieldErrors.trainer && (
              <p className="mt-2 text-sm text-red-600">{fieldErrors.trainer}</p>
            )}
          </div>
        )}

        {selectedRoles.includes('company_manager') && (
          <div className="bg-card rounded-lg border p-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="text-primary h-5 w-5" /> Şirket Bilgileri
              </CardTitle>
            </CardHeader>
            <CompanyForm data={managerData} onChange={onManagerDataChange} />
            {fieldErrors.manager && (
              <p className="mt-2 text-sm text-red-600">{fieldErrors.manager}</p>
            )}
          </div>
        )}

        <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
          <Button variant="outline" onClick={onBack} size="lg">
            <ArrowLeft className="mr-2 h-4 w-4" /> Geri Dön
          </Button>
          <Button
            onClick={onContinue}
            disabled={isSubmitting || !isFormValid}
            size="lg"
          >
            {isSubmitting ? 'Kaydediliyor...' : 'Gözden Geçir'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </>
  );
}
