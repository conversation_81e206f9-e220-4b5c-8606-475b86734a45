import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from './types';
import { env } from '@/lib/env';
import { SupabaseClient } from '@supabase/supabase-js';

export async function createClient(): Promise<SupabaseClient<Database>> {
  const cookieStore = await cookies();

  return createServerClient<Database>(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          );
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  });
}

// Cookie erişimi olmadan, yalnızca public okuma işlemleri için kull<PERSON>ı<PERSON>k client
export async function createAnonClient(): Promise<SupabaseClient<Database>> {
  return createServerClient<Database>(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, {
    cookies: {
      getAll() {
        // PPR sırasında cookie erişimini tetiklememek için boş döndür
        return [] as any;
      },
      setAll() {
        // Public okumalarda cookie yazımı beklenmez; no-op
      },
    },
  });
}
