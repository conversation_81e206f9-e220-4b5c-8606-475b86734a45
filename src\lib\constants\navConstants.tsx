import {
  Home,
  Building,
  Mail,
  LineChart,
  Users,
  Dumbbell,
  Package,
  UserCheck,
  BarChart3,
  Calendar,
  Settings,
  Plus,
  Wrench,
  PackageOpen,
} from 'lucide-react';

// Navigation types
export interface NavigationItem {
  href: string;
  title: string;
  icon: React.ReactNode;
  isActive?: boolean;
}

// Member Navigation Items
export const memberNavigation: NavigationItem[] = [
  {
    href: '/dashboard/member',
    title: '<PERSON><PERSON>',
    icon: <Home className="h-5 w-5" />,
  },
  {
    href: '/dashboard/member/memberships',
    title: 'Üyeliklerim',
    icon: <Building className="h-5 w-5" />,
  },
  {
    href: '/dashboard/member/invitations',
    title: 'Salon Davetleri',
    icon: <Mail className="h-5 w-5" />,
  },
  {
    href: '/dashboard/member/progress',
    title: '<PERSON><PERSON><PERSON><PERSON>',
    icon: <LineChart className="h-5 w-5" />,
  },
];

// Company Navigation Items (Tek şirket mantığı)
export const managerNavigation: NavigationItem[] = [
  {
    href: '/dashboard/company',
    title: 'Şirket Genel Bakış',
    icon: <Home className="h-5 w-5" />,
  },
  {
    href: '/dashboard/company/gym-setup',
    title: 'Yeni Şube Ekle',
    icon: <Plus className="h-5 w-5" />,
  },
  {
    href: '/dashboard/company/managers',
    title: 'Yönetici Davetleri',
    icon: <Mail className="h-5 w-5" />,
  },
  {
    href: '/dashboard/company/settings',
    title: 'Şirket Ayarları',
    icon: <Settings className="h-5 w-5" />,
  },
];

// Unified Gym-specific Navigation Items (for all authorized roles)
export const getGymNavigation = (
  gymId: string,
  role: SidebarMode
): NavigationItem[] => {
  const allItems: NavigationItem[] = [
    {
      href: `/dashboard/gym/${gymId}`,
      title: 'Genel Bakış',
      icon: <Home className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/members`,
      title: 'Üyeler',
      icon: <Users className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/appointments`,
      title: 'Randevular',
      icon: <Calendar className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/trainers`,
      title: 'Antrenörler',
      icon: <Dumbbell className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/packages`,
      title: 'Paketler',
      icon: <Package className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/invitations`,
      title: 'Davetler',
      icon: <Mail className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/staff`,
      title: 'Personel',
      icon: <UserCheck className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/equipment`,
      title: 'Ekipmanlar',
      icon: <Wrench className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/inventory`,
      title: 'Depo',
      icon: <PackageOpen className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/analytics`,
      title: 'Analitik',
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      href: `/dashboard/gym/${gymId}/settings`,
      title: 'Şube Ayarları',
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  // Filter navigation items based on the user's role within the gym
  if (role === 'trainer') {
    const restrictedTitles = [
      'Antrenörler',
      'Personel',
      'Analitik',
      'Şube Ayarları',
    ];
    return allItems.filter(item => !restrictedTitles.includes(item.title));
  }

  return allItems;
};

// Sidebar Navigation Helper Function
export type SidebarMode =
  | 'member'
  | 'company_manager'
  | 'trainer'
  | 'gym_manager';

export const getSidebarNavigation = (
  mode: SidebarMode,
  gymId?: string
): NavigationItem[] => {
  switch (mode) {
    case 'member':
      return memberNavigation;
    case 'company_manager':
      return gymId ? getGymNavigation(gymId, mode) : managerNavigation;
    case 'trainer':
      if (gymId) {
        return getGymNavigation(gymId, mode);
      }
      // Trainer varsayılan navigasyon (kendi paneli ve davetleri)
      return [
        {
          href: '/dashboard/trainer',
          title: 'Salonlarım',
          icon: <Building className="h-5 w-5" />,
        },
        {
          href: '/dashboard/trainer/invitations',
          title: 'Davetler',
          icon: <Mail className="h-5 w-5" />,
        },
      ];
    case 'gym_manager':
      if (gymId) {
        return getGymNavigation(gymId, mode);
      }
      // Gym manager varsayılan navigasyon (yönettiği salonların listesi)
      return [
        {
          href: '/dashboard/gym_manager',
          title: 'Salonlarım',
          icon: <Building className="h-5 w-5" />,
        },
      ];
    default:
      return [];
  }
};
